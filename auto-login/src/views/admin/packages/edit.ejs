<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Edit Package</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Edit Package</h1>
        <div>
          <button id="viewBtn" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded mr-2">
            View Package
          </button>
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Packages
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg p-6">
        <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>
        <div id="successMessage" class="hidden mb-4 p-4 text-green-700 bg-green-100 rounded-md"></div>

        <form id="editPackageForm" class="space-y-4">
          <!-- Package ID (hidden) -->
          <input type="hidden" id="packageId" name="packageId" value="<%= id %>" />

          <!-- Package Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Package Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Premium Package"
            />
          </div>

          <!-- Description -->
          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              id="description"
              name="description"
              rows="3"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Package description..."
            ></textarea>
          </div>

          <!-- No price or duration_days fields as they've been removed from the Package entity -->

          <!-- Is Best Choice -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="is_best_choice"
              name="is_best_choice"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="is_best_choice" class="ml-2 block text-sm text-gray-700">
              Mark as Best Choice
            </label>
          </div>

          <!-- Has Trail -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="has_trail"
              name="has_trail"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="has_trail" class="ml-2 block text-sm text-gray-700">
              Has Trial
            </label>
          </div>

          <!-- Has Prompt Library -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="has_prompt_library"
              name="has_prompt_library"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="has_prompt_library" class="ml-2 block text-sm text-gray-700">
              Has Prompt Library
            </label>
          </div>

          <!-- Has Prompt Video -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="has_prompt_video"
              name="has_prompt_video"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="has_prompt_video" class="ml-2 block text-sm text-gray-700">
              Has Prompt Video
            </label>
          </div>

          <!-- Is Single Tool -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="is_single_tool"
              name="is_single_tool"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="is_single_tool" class="ml-2 block text-sm text-gray-700">
              Is Single Tool
            </label>
          </div>

          <!-- Sort Order -->
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700">Sort Order</label>
            <input
              type="number"
              id="sort"
              name="sort"
              min="0"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="0"
            />
            <p class="mt-1 text-xs text-gray-500">Lower numbers appear first</p>
          </div>

          <!-- Package Image -->
          <div>
            <label for="pk_img" class="block text-sm font-medium text-gray-700">Package Image</label>
            <div class="mt-1 flex items-center space-x-2">
              <input
                type="file"
                id="pk_img_file"
                accept="image/*"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <input
                type="hidden"
                id="pk_img"
                name="pk_img"
              />
            </div>
            <div class="mt-2">
              <img
                id="pk_img_preview"
                src=""
                class="hidden h-32 object-contain border rounded"
                alt="Package image preview"
              />
            </div>
            <p class="mt-1 text-xs text-gray-500">Upload an image for this package (max 1MB)</p>
          </div>

          <!-- Package Features -->
          <div>
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">Features</label>
              <button
                type="button"
                id="addFeatureBtn"
                class="bg-blue-500 hover:bg-blue-700 text-white text-sm py-1 px-2 rounded"
              >
                Add Feature
              </button>
            </div>
            <div id="featuresContainer" class="space-y-2">
              <!-- Feature items will be added here dynamically -->
            </div>
          </div>

          <!-- Package Durations -->
          <div>
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">Additional Durations</label>
              <button
                type="button"
                id="addDurationBtn"
                class="bg-blue-500 hover:bg-blue-700 text-white text-sm py-1 px-2 rounded"
              >
                Add Duration
              </button>
            </div>
            <div id="durationsContainer" class="space-y-3">
              <!-- Duration items will be added here dynamically -->
            </div>
          </div>

          <!-- Payment Method Discounts Section -->
          <div class="mt-6">
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">Payment Method Discounts</label>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-600 mb-3">Set discount percentages for different payment methods</p>
              <div id="paymentMethodsContainer" class="space-y-3">
                <!-- Payment method discount inputs will be added here -->
              </div>
            </div>
          </div>

          <div class="pt-4">
            <button
              type="submit"
              class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Update Package
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Global variables
      const packageId = `<%= id %>`;

      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show error message
      function showError(message) {
        const errorElement = document.getElementById('errorMessage');
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');

        // Hide success message if it's visible
        document.getElementById('successMessage').classList.add('hidden');

        // Scroll to error message
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      // Show success message
      function showSuccess(message) {
        const successElement = document.getElementById('successMessage');
        successElement.textContent = message;
        successElement.classList.remove('hidden');

        // Hide error message if it's visible
        document.getElementById('errorMessage').classList.add('hidden');

        // Scroll to success message
        successElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      // Fetch package data
      async function fetchPackage(id) {
        if (!setupAxios()) return null;

        try {
          const response = await axios.get(`/packages/${id}`);
          return response.data;
        } catch (error) {
          console.error('Error fetching package:', error);
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else if (error.response && error.response.status === 404) {
            showError('Package not found');
            setTimeout(() => {
              window.location.href = '/admin/packages';
            }, 2000);
          } else {
            const message = error.response?.data?.message || 'Failed to fetch package data. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Update package
      async function updatePackage(id, formData) {
        if (!setupAxios()) return;

        try {
          const response = await axios.patch(`/packages/${id}`, formData);
          showSuccess('Package updated successfully!');
          return response.data;
        } catch (error) {
          console.error('Error updating package:', error);
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to update package. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Function to update discount price display
      function updateDiscountPrice(input) {
        const durationItem = input.closest('.duration-item');
        const priceInput = durationItem.querySelector('.duration-price');
        const discountInput = durationItem.querySelector('.duration-discount');
        const discountPriceDisplay = durationItem.querySelector('.discount-price-display');

        const price = parseFloat(priceInput.value);
        const discountPercent = parseFloat(discountInput.value) || 0;

        if (!isNaN(price) && price > 0) {
          const discountedPrice = (price * (1 - discountPercent / 100)).toFixed(2);
          discountPriceDisplay.textContent = '$' + discountedPrice;

          // Add visual indication if there's a discount
          if (discountPercent > 0) {
            discountPriceDisplay.classList.add('text-green-600', 'font-medium');

            // If the discounted price is the same as the original price (due to rounding),
            // still show it differently to indicate the discount is applied
            if (parseFloat(discountedPrice) === price) {
              discountPriceDisplay.classList.add('text-green-600', 'font-medium');
            }
          } else {
            discountPriceDisplay.classList.remove('text-green-600', 'font-medium');
          }
        } else {
          discountPriceDisplay.textContent = '-';
          discountPriceDisplay.classList.remove('text-green-600', 'font-medium');
        }
      }

      // Function to handle image selection and convert to base64
      function handleImageSelect(event, imgPreview, imgInput) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file size (limit to 1MB)
        if (file.size > 1024 * 1024) {
          showError('Image size should be less than 1MB');
          event.target.value = '';
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          const base64String = e.target.result;
          imgPreview.src = base64String;
          imgPreview.classList.remove('hidden');
          imgInput.value = base64String;
        };
        reader.readAsDataURL(file);
      }

      // Function to add a new feature item
      function addFeatureItem(feature = { description: '', img: '' }) {
        const featuresContainer = document.getElementById('featuresContainer');
        const featureIndex = document.querySelectorAll('.feature-item').length;
        const featureId = `feature-${featureIndex}`;

        const featureItem = document.createElement('div');
        featureItem.className = 'feature-item flex flex-col space-y-2 p-3 border border-gray-300 rounded-md';
        featureItem.innerHTML = `
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <label class="block text-xs text-gray-500">Feature Description</label>
              <input
                type="text"
                class="feature-description w-full px-2 py-1 border border-gray-300 rounded"
                value="${feature.description}"
                placeholder="Feature description"
              />
            </div>
            <div>
              <button
                type="button"
                class="remove-feature-btn bg-red-500 hover:bg-red-700 text-white text-sm py-1 px-2 rounded"
              >
                Remove
              </button>
            </div>
          </div>
          <div>
            <label class="block text-xs text-gray-500">Feature Image</label>
            <div class="flex items-center space-x-2">
              <input
                type="file"
                accept="image/*"
                class="feature-image-file w-full px-2 py-1 border border-gray-300 rounded"
                id="${featureId}-file"
              />
              <input
                type="hidden"
                class="feature-image-data"
                value="${feature.img || ''}"
              />
            </div>
            <div class="mt-2">
              <img
                src="${feature.img || ''}"
                class="feature-image-preview ${feature.img ? '' : 'hidden'} h-20 object-contain border rounded"
              />
            </div>
          </div>
        `;

        featuresContainer.appendChild(featureItem);

        // Add event listener to the remove button
        featureItem.querySelector('.remove-feature-btn').addEventListener('click', () => {
          featureItem.remove();
        });

        // Add event listener to the file input
        const fileInput = featureItem.querySelector('.feature-image-file');
        const imgPreview = featureItem.querySelector('.feature-image-preview');
        const imgInput = featureItem.querySelector('.feature-image-data');

        fileInput.addEventListener('change', (e) => {
          handleImageSelect(e, imgPreview, imgInput);
        });
      }

      // Populate form with package data
      function populateForm(package_) {
        document.getElementById('name').value = package_.name || '';
        document.getElementById('description').value = package_.description || '';

        // Populate new fields
        document.getElementById('is_best_choice').checked = package_.is_best_choice || false;
        document.getElementById('has_trail').checked = package_.has_trail || false;
        document.getElementById('has_prompt_library').checked = package_.has_prompt_library || false;
        document.getElementById('has_prompt_video').checked = package_.has_prompt_video || false;
        document.getElementById('is_single_tool').checked = package_.is_single_tool || false;
        document.getElementById('sort').value = package_.sort || 0;

        // Populate package image
        if (package_.pk_img) {
          document.getElementById('pk_img').value = package_.pk_img;
          const imgPreview = document.getElementById('pk_img_preview');
          imgPreview.src = package_.pk_img;
          imgPreview.classList.remove('hidden');
        }

        // Populate features
        if (package_.features && package_.features.length > 0) {
          // Clear existing features
          document.getElementById('featuresContainer').innerHTML = '';

          // Add each feature to the form
          package_.features.forEach(feature => {
            // Handle both old string format and new object format
            if (typeof feature === 'string') {
              addFeatureItem({ description: feature, img: '' });
            } else {
              addFeatureItem(feature);
            }
          });
        }

        // Populate durations
        if (package_.durations && package_.durations.length > 0) {
          // Clear existing durations
          document.getElementById('durationsContainer').innerHTML = '';

          // Sort durations by duration_days
          const sortedDurations = [...package_.durations].sort((a, b) => a.duration_days - b.duration_days);

          // Add each duration to the form
          sortedDurations.forEach(duration => {
            addDurationItem(
              duration.id,
              duration.duration_days,
              duration.price,
              duration.discount_percent || 0
            );
          });
        }
      }

      // Function to add a new duration item
      function addDurationItem(id = '', durationDays = '', price = '', discountPercent = '') {
        const durationsContainer = document.getElementById('durationsContainer');
        const durationIndex = document.querySelectorAll('.duration-item').length;

        const durationItem = document.createElement('div');
        durationItem.className = 'duration-item flex items-center space-x-2 p-3 border border-gray-300 rounded-md';

        // Add hidden input for duration ID if it exists
        const idInput = id ? `<input type="hidden" class="duration-id" value="${id}">` : '';

        durationItem.innerHTML = `
          ${idInput}
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Duration (Days)</label>
            <input
              type="number"
              class="duration-days w-full px-2 py-1 border border-gray-300 rounded"
              min="1"
              value="${durationDays}"
              placeholder="30"
              required
            />
          </div>
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Price ($)</label>
            <input
              type="number"
              class="duration-price w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              step="0.01"
              value="${price}"
              placeholder="9.99"
              required
              oninput="updateDiscountPrice(this)"
            />
          </div>
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Discount (%)</label>
            <input
              type="number"
              class="duration-discount w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              max="100"
              step="0.01"
              value="${discountPercent}"
              placeholder="0"
              oninput="updateDiscountPrice(this)"
            />
          </div>
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Discount Price ($)</label>
            <div class="discount-price-display px-2 py-1 bg-gray-100 rounded">
              -
            </div>
          </div>
          <div class="flex items-end pb-1">
            <button
              type="button"
              class="remove-duration-btn bg-red-500 hover:bg-red-700 text-white text-sm py-1 px-2 rounded"
            >
              Remove
            </button>
          </div>
        `;

        durationsContainer.appendChild(durationItem);

        // Add event listener to the remove button
        durationItem.querySelector('.remove-duration-btn').addEventListener('click', () => {
          durationItem.remove();
        });

        // Always initialize discount price display
        setTimeout(() => {
          updateDiscountPrice(durationItem.querySelector('.duration-price'));
        }, 0);
      }

      // Load payment methods and existing discounts
      async function loadPaymentMethodsWithDiscounts() {
        try {
          // Load payment methods
          const paymentMethodsResponse = await axios.get('/packages/payment-methods');
          const paymentMethods = paymentMethodsResponse.data;

          // Load existing package discounts
          const packageDiscountsResponse = await axios.get(`/packages/${packageId}/discounts`);
          const packageDiscounts = packageDiscountsResponse.data;

          // Create a map of existing discounts by payment method ID
          const existingDiscounts = {};
          packageDiscounts.forEach(discount => {
            existingDiscounts[discount.payment_method_id] = discount.discount_percent;
          });

          const container = document.getElementById('paymentMethodsContainer');
          container.innerHTML = '';

          paymentMethods.forEach(method => {
            const existingDiscount = existingDiscounts[method.id] || 0;
            const methodDiv = document.createElement('div');
            methodDiv.className = 'flex items-center justify-between p-3 border border-gray-300 rounded-md bg-white';
            methodDiv.innerHTML = `
              <div class="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="method_${method.id}"
                  name="payment_methods[]"
                  value="${method.id}"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  ${existingDiscount > 0 ? 'checked' : ''}
                />
                <label for="method_${method.id}" class="text-sm font-medium text-gray-700">
                  ${method.name.charAt(0).toUpperCase() + method.name.slice(1)}
                </label>
                <span class="text-xs text-gray-500">${method.description || ''}</span>
              </div>
              <div class="flex items-center space-x-2">
                <label for="discount_${method.id}" class="text-sm text-gray-600">Discount %:</label>
                <input
                  type="number"
                  id="discount_${method.id}"
                  name="discount_${method.id}"
                  value="${existingDiscount}"
                  min="0"
                  max="100"
                  step="0.01"
                  class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  placeholder="0"
                />
              </div>
            `;
            container.appendChild(methodDiv);

            // Add event listener to checkbox to enable/disable discount input
            const checkbox = methodDiv.querySelector(`#method_${method.id}`);
            const discountInput = methodDiv.querySelector(`#discount_${method.id}`);

            checkbox.addEventListener('change', function() {
              if (this.checked) {
                discountInput.disabled = false;
                if (discountInput.value === '0' || discountInput.value === '') {
                  discountInput.value = '5'; // Default 5% discount
                }
              } else {
                discountInput.disabled = true;
                discountInput.value = '0';
              }
            });

            // Initial state
            if (!checkbox.checked) {
              discountInput.disabled = true;
            }
          });
        } catch (error) {
          console.error('Error loading payment methods:', error);
          handleApiError(error);
        }
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        setupAxios();
        if (!(await checkAuth())) return;

        // Fetch and populate package data
        const package_ = await fetchPackage(packageId);
        if (package_) {
          populateForm(package_);
        }

        // Load payment methods and existing discounts
        await loadPaymentMethodsWithDiscounts();

        // View button
        document.getElementById('viewBtn').addEventListener('click', () => {
          window.location.href = `/admin/packages/${packageId}`;
        });

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/packages';
        });

        // Add feature button
        document.getElementById('addFeatureBtn').addEventListener('click', () => {
          addFeatureItem();
        });

        // Add duration button
        document.getElementById('addDurationBtn').addEventListener('click', () => {
          addDurationItem();
        });

        // Package image upload
        document.getElementById('pk_img_file').addEventListener('change', (e) => {
          const imgPreview = document.getElementById('pk_img_preview');
          const imgInput = document.getElementById('pk_img');
          handleImageSelect(e, imgPreview, imgInput);
        });

        // Form submission
        document.getElementById('editPackageForm').addEventListener('submit', async (e) => {
          e.preventDefault();

          // Get all duration items
          const durationItems = document.querySelectorAll('.duration-item');
          const durations = [];

          // Extract data from each duration item
          durationItems.forEach(item => {
            const durationId = item.querySelector('.duration-id')?.value;
            const durationDays = parseInt(item.querySelector('.duration-days').value, 10);
            const price = parseFloat(item.querySelector('.duration-price').value);
            const discountPercent = parseFloat(item.querySelector('.duration-discount').value) || 0;

            if (!isNaN(durationDays) && !isNaN(price)) {
              const duration = {
                duration_days: durationDays,
                price: price,
                discount_percent: discountPercent
              };

              // Add ID if it exists (for updating existing durations)
              if (durationId) {
                duration.id = parseInt(durationId, 10);
              }

              durations.push(duration);
            }
          });

          // Get all feature items
          const featureItems = document.querySelectorAll('.feature-item');
          const features = [];

          // Extract data from each feature item
          featureItems.forEach(item => {
            const description = item.querySelector('.feature-description').value.trim();
            const img = item.querySelector('.feature-image-data').value;

            if (description) {
              features.push({
                description,
                img: img || ''
              });
            }
          });

          // Get payment method discounts
          const paymentMethodDiscounts = [];
          document.querySelectorAll('input[name="payment_methods[]"]:checked').forEach(checkbox => {
            const methodId = parseInt(checkbox.value);
            const discountInput = document.querySelector(`input[name="discount_${methodId}"]`);
            const discountPercent = parseFloat(discountInput.value) || 0;

            // Always include checked payment methods (even with 0 discount) for update
            paymentMethodDiscounts.push({
              payment_method_id: methodId,
              discount_percent: discountPercent
            });
          });

          // Create a direct JavaScript object
          const formData = {
            name: document.getElementById('name').value,
            description: document.getElementById('description').value || '',
            is_best_choice: document.getElementById('is_best_choice').checked,
            has_trail: document.getElementById('has_trail').checked,
            has_prompt_library: document.getElementById('has_prompt_library').checked,
            has_prompt_video: document.getElementById('has_prompt_video').checked,
            is_single_tool: document.getElementById('is_single_tool').checked,
            sort: parseInt(document.getElementById('sort').value) || 0,
            pk_img: document.getElementById('pk_img').value || null,
            features: features,
            durations: durations,
            discounts: paymentMethodDiscounts
          };

          await updatePackage(packageId, formData);
        });
      });
    </script>
  </body>
</html>
